#!/usr/bin/env python3
"""
Test to verify that SmartServiceProxy doesn't create duplicate RPC services.
"""

import time
from DistributedBus import DistributedBus, service


@service("TestDuplicateService")
class TestDuplicateService:
    """A simple test service."""
    
    def __init__(self):
        self.instance_id = id(self)
    
    def get_instance_id(self):
        """Return the instance ID to verify we're using the same instance."""
        return self.instance_id
    
    def ping(self):
        return f"pong from instance {self.instance_id}"


def test_no_duplicate_services():
    """Test that SmartServiceProxy doesn't create duplicate services."""
    print("Testing that SmartServiceProxy doesn't create duplicate services...")
    
    # Create a bus instance
    bus = DistributedBus()
    bus.start()
    
    try:
        # Wait for service registration
        time.sleep(2)
        
        # Check initial state
        node_info = bus.get_node_info()
        print(f"Initial local services: {node_info['local_services']}")
        
        # Count initial RPC servers
        initial_server_count = len(bus.rpc_server.servers)
        print(f"Initial RPC server count: {initial_server_count}")
        
        # Get service through SmartServiceProxy multiple times
        print("\nFirst lookup through SmartServiceProxy...")
        proxy1 = bus.lookup("TestDuplicateService")
        result1 = proxy1.get_instance_id()
        print(f"First result: {result1}")
        
        # Check server count after first lookup
        after_first_count = len(bus.rpc_server.servers)
        print(f"RPC server count after first lookup: {after_first_count}")
        
        print("\nSecond lookup through SmartServiceProxy...")
        proxy2 = bus.lookup("TestDuplicateService")
        result2 = proxy2.get_instance_id()
        print(f"Second result: {result2}")
        
        # Check server count after second lookup
        after_second_count = len(bus.rpc_server.servers)
        print(f"RPC server count after second lookup: {after_second_count}")
        
        print("\nThird lookup through SmartServiceProxy...")
        proxy3 = bus.lookup("TestDuplicateService")
        result3 = proxy3.get_instance_id()
        print(f"Third result: {result3}")
        
        # Check final server count
        final_count = len(bus.rpc_server.servers)
        print(f"Final RPC server count: {final_count}")
        
        # Verify results
        success = True
        
        # Should have exactly one service
        if final_count != 1:
            print(f"✗ FAIL: Expected 1 RPC server, got {final_count}")
            success = False
        else:
            print("✓ PASS: Only one RPC server created")
        
        # All lookups should return the same instance
        if result1 == result2 == result3:
            print("✓ PASS: All lookups returned the same service instance")
        else:
            print(f"✗ FAIL: Different instances returned: {result1}, {result2}, {result3}")
            success = False
        
        # Server count should not increase after first lookup
        if after_first_count == after_second_count == final_count:
            print("✓ PASS: No additional RPC servers created after first lookup")
        else:
            print(f"✗ FAIL: RPC server count changed: {after_first_count} -> {after_second_count} -> {final_count}")
            success = False
        
        return success
        
    finally:
        bus.close()


def test_service_reuse():
    """Test that existing services are properly reused."""
    print("\n" + "="*60)
    print("Testing service reuse...")
    
    bus = DistributedBus()
    bus.start()
    
    try:
        time.sleep(2)
        
        # Get the service instance directly from the bus
        direct_service = bus.rpc_server.services.get("TestDuplicateService")
        if direct_service:
            direct_id = direct_service.get_instance_id()
            print(f"Direct service instance ID: {direct_id}")
        else:
            print("No direct service found")
            return False
        
        # Get service through SmartServiceProxy
        smart_proxy = bus.lookup("TestDuplicateService")
        proxy_result = smart_proxy.get_instance_id()
        print(f"SmartServiceProxy result: {proxy_result}")
        
        # They should be the same
        if direct_id == proxy_result:
            print("✓ PASS: SmartServiceProxy reused existing service instance")
            return True
        else:
            print("✗ FAIL: SmartServiceProxy created new instance instead of reusing existing")
            return False
            
    finally:
        bus.close()


if __name__ == "__main__":
    print("Testing duplicate service creation fix...")
    
    success1 = test_no_duplicate_services()
    success2 = test_service_reuse()
    
    if success1 and success2:
        print("\n✓ All tests passed! Duplicate service issue is fixed.")
    else:
        print("\n✗ Some tests failed!")
